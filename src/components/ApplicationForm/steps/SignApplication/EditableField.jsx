import { useState } from "react";
import { useController, useFormContext } from "react-hook-form";
import { getValueByPath } from "../../../../utils/formatters";

export const EditableField = ({
  label,
  type,
  className,
  fieldName,
  formatter = (v) => v,
  placeholder = "N/A",
  options,
  inputProps,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  // grab value from form
  const { watch, formState } = useFormContext();
  const value = watch(fieldName);

  const hasError = getValueByPath(formState.errors, fieldName);

  return (
    <>
      <span className={`font-medium text-slate-500 mb-1 ${hasError ? "text-red-600" : ""}`}>{label}</span>
      {isEditing ? (
        <FormattedInput
          type={type}
          fieldName={fieldName}
          options={options}
          setIsEditing={setIsEditing}
          inputProps={inputProps}
        />
      ) : (
        <div className="inline-block" id={`${fieldName}-container`}>
          <span
            className={`${className} cursor-pointer hover:bg-blue-50 hover:text-blue-700 rounded px-1 py-0.5 transition-colors ${
              hasError ? "text-red-600 bg-red-50" : ""
            }`}
            onClick={() => setIsEditing(true)}
            title="Click to edit"
          >
            {formatter(value) || placeholder}
          </span>
        </div>
      )}
    </>
  );
};

const FormattedInput = ({ type = "text", fieldName, options, setIsEditing, inputProps = {} }) => {
  const {
    field: { onChange, onBlur, ...fieldProps },
    formState,
  } = useController({ name: fieldName });
  const hasError = getValueByPath(formState.errors, fieldName);

  if (type == "select") {
    return (
      <select
        className={`inline-block min-w-0 bg-white border rounded px-1 py-0.5 text-sm focus:outline-none focus:ring-1 max-w-[240px] ${
          hasError
            ? "border-red-500 focus:ring-red-500 focus:border-red-500"
            : "border-blue-500 focus:ring-blue-500 focus:border-blue-500"
        }`}
        autoFocus
        name={fieldName}
        {...fieldProps}
        onBlur={(e) => {
          setIsEditing(false);
          onBlur(e);
        }}
        onChange={(e) => {
          onChange(e);
          setIsEditing(false);
        }}
      >
        {options?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  }

  const { formatter, parser, ...restInputProps } = inputProps;
  const displayValue = formatter ? formatter(fieldProps.value) : fieldProps.value;
  return (
    <input
      name={fieldName}
      {...fieldProps}
      value={displayValue}
      type={type}
      className={`inline-block min-w-0 bg-white border rounded px-1 py-0.5 text-sm focus:outline-none focus:ring-1 ${
        hasError
          ? "border-red-500 focus:ring-red-500 focus:border-red-500"
          : "border-blue-500 focus:ring-blue-500 focus:border-blue-500"
      }`}
      autoFocus
      {...restInputProps}
      onChange={(e) => {
        if (parser) {
          e.target.value = parser(e.target.value);
        }

        onChange(e.target.value);
      }}
      onBlur={(e) => {
        setIsEditing(false);
        onBlur(e);
      }}
    />
  );
};
