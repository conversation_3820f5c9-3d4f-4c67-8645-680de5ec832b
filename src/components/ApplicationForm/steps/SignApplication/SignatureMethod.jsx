import { useEffect, useRef, useState, forwardRef, useImperativeHandle, useCallback } from "react";
import "@fontsource/courgette/400.css";
import SignatureCanvas from "react-signature-canvas";
import { Trash2 } from "lucide-react";
import { useFormContext } from "react-hook-form";

const TypedSignatureCanvas = forwardRef(({ value, onChange, placeholder = "Type your name" }, ref) => {
  const canvasRef = useRef(null);
  const [isActive, setIsActive] = useState(false);
  const { clearErrors } = useFormContext();

  const getSignatureDataURL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !value) return null;
    return canvas.toDataURL("image/png");
  }, [value]);

  useImperativeHandle(
    ref,
    () => ({
      getSignatureDataURL,
    }),
    [getSignatureDataURL],
  );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    const rect = canvas.getBoundingClientRect();

    // Set canvas size to match display size
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set font styling to match the input
    ctx.font = "1.875rem Courgette, cursive";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillStyle = value ? "#1e293b" : "#d1d5dc"; // slate-800 or gray-500

    // Draw text
    const text = value || placeholder;
    const x = rect.width / 2;
    const y = rect.height / 2 + 24;

    if (!value) {
      // fix initial font loading
      setTimeout(() => {
        ctx.fillText(text, x, y);
      }, 50);
      ctx.fillText(" ", x, y);
    } else {
      ctx.fillText(text, x, y);
    }
  }, [value, placeholder]);

  const handleCanvasClick = () => {
    setIsActive(true);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Escape") {
      setIsActive(false);
      return;
    }

    if (e.key === "Backspace") {
      onChange(value.slice(0, -1));
    } else if (e.key.length === 1) {
      onChange(value + e.key);
    }
  };

  return (
    <div className="relative w-full h-full">
      <canvas
        ref={canvasRef}
        onClick={handleCanvasClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        className="w-full h-full cursor-text focus:outline-none"
        style={{ width: "100%", height: "100%" }}
      />
      {isActive && (
        <div className="absolute inset-0 top-10 pointer-events-none">
          <input
            type="text"
            value={value}
            onChange={(e) => {
              onChange(e.target.value);
              // Clear signature error when user starts typing
              clearErrors("signature");
            }}
            onBlur={() => setIsActive(false)}
            autoFocus
            className="font-courgette font-medium text-3xl w-full h-full bg-transparent text-transparent caret-slate-800 text-center focus:outline-none"
          />
        </div>
      )}
      {value && (
        <button
          type="button"
          onClick={() => onChange("")}
          className="absolute top-3 right-3 z-20  border-gray-300 rounded-full shadow-sm hover:bg-gray-50 transition-colors"
          title="Clear signature"
        >
          <Trash2 className="text-red-600" />
        </button>
      )}
    </div>
  );
});

const SigCanvas = forwardRef((_, ref) => {
  const [isEmpty, setIsEmpty] = useState(true);
  const signCanvasRef = useRef(null);
  const { clearErrors } = useFormContext();

  const getSignatureDataURL = useCallback(() => {
    if (!signCanvasRef.current || isEmpty) return null;
    return signCanvasRef.current.toDataURL("image/png");
  }, [isEmpty]);

  useImperativeHandle(
    ref,
    () => ({
      getSignatureDataURL,
    }),
    [getSignatureDataURL],
  );

  useEffect(() => {
    const canvasRef = signCanvasRef.current;

    if (canvasRef) {
      canvasRef.clear();

      canvasRef._sigPad.onBegin = () => {
        setIsEmpty(false);
        // Clear signature error when user starts drawing
        clearErrors("signature");
      };

      setIsEmpty(true);
    }

    return () => {
      // canvasRef?.off();
    };
  }, []);

  const clearCanvas = () => {
    if (signCanvasRef.current) {
      signCanvasRef.current.clear();
      setIsEmpty(true);
    }
  };

  return (
    <div className="relative font-courgette text-5xl w-full h-full flex items-center justify-center text-gray-500">
      <SignatureCanvas
        ref={(ref) => {
          signCanvasRef.current = ref;
        }}
        canvasProps={{ className: "relative z-0 bg-white", style: { width: "100%", height: "100%" } }}
        backgroundColor="white"
      />
      {isEmpty && (
        <span className="absolute select-none z-10 left-1/2 top-22  transform -translate-x-1/2 text-gray-300 text-3xl">
          Sign here
        </span>
      )}
      {!isEmpty && (
        <button
          type="button"
          onClick={clearCanvas}
          className="absolute top-2 right-2 z-20 p-2 border-gray-300 rounded-full hover:bg-gray-50 transition-colors"
          title="Clear signature"
        >
          <Trash2 className="text-red-600" />
        </button>
      )}
    </div>
  );
});

const SignatureMethod = forwardRef((_, ref) => {
  const [typedSignature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // Track signature method
  const typedCanvasRef = useRef(null);
  const drawCanvasRef = useRef(null);

  // Get form context to check for signature errors
  const { formState, clearErrors } = useFormContext();
  const hasSignatureError = formState.errors?.signature;

  const getSignatureDataURL = useCallback(() => {
    if (signatureMethod === "type") {
      return typedCanvasRef.current?.getSignatureDataURL();
    } else {
      return drawCanvasRef.current?.getSignatureDataURL();
    }
  }, [signatureMethod]);

  useImperativeHandle(
    ref,
    () => ({
      getSignatureDataURL,
    }),
    [getSignatureDataURL],
  );

  const toggleSignatureMethod = () => {
    if (signatureMethod === "type") {
      console.log({ base64: typedCanvasRef.current?.getSignatureDataURL() });
      setSignature("");
    } else {
      console.log({ base64Drawn: drawCanvasRef.current?.getSignatureDataURL() });
    }
    // Clear signature error when user switches signature method
    clearErrors("signature");
    setSignatureMethod(signatureMethod === "type" ? "draw" : "type");
  };

  return (
    <div className="space-y-4">
      <div className="w-full">
        {/* Signature Display Area */}
        <div
          className={`w-full min-w-[350px] lg:w-1/2 h-42 relative border-2 rounded-sm bg-white mb-4 flex items-center justify-center ${
            hasSignatureError ? "border-red-500" : "border-slate-300"
          }`}
        >
          <div id="signature-box" className="relative z-10  w-full h-full flex items-center justify-center ">
            {signatureMethod === "type" ? (
              <TypedSignatureCanvas
                ref={typedCanvasRef}
                value={typedSignature}
                onChange={setSignature}
                placeholder="Type your name"
              />
            ) : (
              <SigCanvas ref={drawCanvasRef} />
            )}
            {/** sign on line */}
            <div className="absolute w-4/5 h-0.5 bottom-8 border-t border-1/2 border-dashed border-gray-400 z-[1]" />
          </div>
        </div>
        {/* Error message */}
        {hasSignatureError && <p className="text-red-600 text-sm mt-1 mb-2">{hasSignatureError.message}</p>}

        {/* Signature Method Buttons */}
        <div className="w-full lg:w-1/2 lg:min-w-[350px] flex gap-4 text-sm">
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-2 rounded-sm border-2 font-medium transition-colors ${
              signatureMethod === "type"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            Type Signature
          </button>
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-2 rounded-sm border-2 font-medium transition-colors ${
              signatureMethod === "draw"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            Draw Signature
          </button>
        </div>
      </div>
      <div className="w-full max-w-xs">
        <label className="block text-sm font-bold text-slate-800 mb-2">Date of Signing: </label>
        <div className="w-full p-3 border border-slate-300 rounded-sm text-sm font-semibold text-slate-800 shadow-sm">
          {new Date().toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </div>
      </div>
    </div>
  );
});

export default SignatureMethod;
