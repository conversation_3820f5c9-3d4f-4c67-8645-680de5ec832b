import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useAppStorage } from "../../hooks/useAppStorage.js";
import { useCreateAppApi } from "../../hooks/useCreateAppApi.js";
import { trackCustomEvent, trackFieldFilled, trackFormSubmitted, trackStepCompleted } from "../../utils/analytics.js";
import { ErrorModal } from "../shared/ErrorModal";
import { LoadingOverlay } from "../shared/LoadingSpinner";
import { ProgressBar } from "../shared/ProgressBar";
import { BusinessInformation } from "./steps/BusinessInformation.jsx";
import { FundingInformation } from "./steps/FundingInformation.jsx";
import { OwnerInformation } from "./steps/OwnerInformation.jsx";
import { DuplicateSubmissionWarningModal } from "../shared/DuplicateSubmissionWarningModal.jsx";

import { useForm } from "react-hook-form";
import { APP_FLOW_STATUS, defaultPreQualifyValues, RECENT_APP, SHOW_DUPLICATE_PAGE_KEY } from "../../utils/consts.js";
import { logger } from "../../utils/logger.js";
import DataSecuredCard from "../shared/DataSecuredCard.jsx";
import { cookiesHandler } from "../../utils/customCookieStorage.js";

const steps = [
  { id: "funding", title: "Funding Information" },
  { id: "business", title: "Business Information" },
  { id: "owner", title: "Owner Information" },
];

const fieldsForStep = {
  0: ["fundingAmount", "purpose", "topPriority", "timeline"],
  1: ["businessName", "monthlyRevenue", "annualRevenue", "businessStartDate", "businessStartConfirmation"],
  2: ["firstName", "lastName", "email", "phone", "estimatedFICO", "consent"],
};

const paramToCurrentStep = {
  "": 0,
  step2: 1,
  step3: 2,
};

export const PreQualificationForm = ({
  header = "Get Pre-Approved In Minutes",
  subheader,
  firstStepTitle = "Let’s Figure out your Funding",
}) => {
  const {
    applicationId: appId,
    preQualifyForm: savedFields,
    setPreQualifyForm: storeFormFields,
    setPreQualifyResult: storePreQualifyResult,
    setApplicationId: storeAppID,
    clearAllData,
  } = useAppStorage();

  const params = useParams();
  const location = useLocation();

  if (appId) {
    clearAllData();
    storeFormFields(defaultPreQualifyValues);
  }

  const { error: errorMessage, errorId, createApp, status } = useCreateAppApi();

  const [showErrorModal, setShowErrorModal] = useState(false);
  const navigate = useNavigate();

  // URL is now the source of truth for current step
  const currentStep = paramToCurrentStep[params.step] || 0;
  const appInit = useRef(false);

  // Helper function to build navigation paths based on current location
  const getStepPath = useCallback(
    (step) => {
      const basePath = location.pathname.replace(/\/(step[23])?$/, "");
      if (step === 0) {
        return basePath || "/";
      } else if (step === 1) {
        return `${basePath}/step2`;
      } else if (step === 2) {
        return `${basePath}/step3`;
      }
      return basePath || "/";
    },
    [location.pathname],
  );

  // Derive formStatus from status
  const formStatus = status === "loading" ? "saving" : "idle";

  const formMethods = useForm({
    defaultValues: defaultPreQualifyValues,
    mode: "onSubmit",
    reValidateMode: "onBlur",
    criteriaMode: "all",
  });

  useEffect(() => {
    if (appInit.current) return;
    // Only track if this is a new form (no appId)
    if (!appId) {
      if (savedFields) {
        Object.keys(savedFields).forEach((key) => {
          trackFieldFilled(key, savedFields[key], "PreQualification");
        });
      }

      formMethods.reset(savedFields);
      appInit.current = true;
    }
  }, [appId, formMethods, savedFields]);

  const switchToInvalidStep = useCallback(async () => {
    const [step1Valid, step2Valid, step3Valid] = await Promise.all([
      formMethods.trigger(fieldsForStep[0]),
      formMethods.trigger(fieldsForStep[1]),
      formMethods.trigger(fieldsForStep[2]),
    ]);

    if (!step1Valid) {
      navigate(getStepPath(0), { replace: true });
    } else if (!step2Valid) {
      navigate(getStepPath(1), { replace: true });
    } else if (!step3Valid) {
      navigate(getStepPath(2), { replace: true });
    }
  }, [formMethods, navigate, getStepPath]);

  // Handle next step button click
  const handleNextStep = async () => {
    const isValid = await formMethods.trigger(fieldsForStep[currentStep]);

    const errors = formMethods.formState.errors;

    if (!isValid) {
      for (const field of fieldsForStep[currentStep]) {
        const val = errors[field];

        if (val) {
          const element = document.getElementsByName(field)[0];
          if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
            setTimeout(() => {
              element.focus();
            }, 500);
          }
          break;
        }
      }
      return;
    }

    if (isValid) {
      // Track step completion
      const completedStep = steps[currentStep].title;
      trackStepCompleted(completedStep, "PreQualification");

      const nextStep = Math.min(steps.length - 1, currentStep + 1);

      // Navigate to the next step URL
      navigate(getStepPath(nextStep));

      formMethods.setValue("currentStep", nextStep);
      storeFormFields(formMethods.getValues());

      // Scroll after a brief delay to allow navigation to complete
      setTimeout(() => {
        const scrollTarget = document.getElementById("prequalify-form-scroll-target") || document.forms[0];
        scrollTarget.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  };

  // Handle previous step button click
  const handlePrevStep = () => {
    const prevStep = Math.max(0, currentStep - 1);

    // Navigate to the previous step URL
    if (prevStep === 0) {
      navigate("/");
    } else if (prevStep === 1) {
      navigate("/step2");
    }

    formMethods.setValue("currentStep", prevStep);
    storeFormFields(formMethods.getValues());

    // Scroll after a brief delay to allow navigation to complete
    setTimeout(() => {
      const scrollTarget = document.getElementById("prequalify-form-scroll-target") || document.forms[0];
      scrollTarget.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  // Handle error modal close
  const handleCloseErrorModal = () => {
    setShowErrorModal(false);
  };

  // Handle form submission
  const handleFormSubmit = async (data) => {
    // Save form values to cookies before submitting
    storeFormFields(data);

    // Track form submission
    trackFormSubmitted("PreQualification", {
      fundingAmount: data.fundingAmount,
      purpose: data.purpose,
      topPriority: data.topPriority,
      timeline: data.timeline,
      monthlyRevenue: data.monthlyRevenue,
      businessStartDate: data.businessStartDate,
      estimatedFICO: data.estimatedFICO,
    });

    try {
      // Use our custom hook to create the app

      sessionStorage.setItem(SHOW_DUPLICATE_PAGE_KEY, "false");

      const result = await createApp(data);

      if (result.duplicate) {
        sessionStorage.setItem(SHOW_DUPLICATE_PAGE_KEY, "true");
        navigate(`/prequalify-result/${result.uuid}`, { replace: true });
        return;
      }

      cookiesHandler.set(RECENT_APP, result, { days: 3 });

      // Only store the application ID if the status is PREQUAL_APPROVED
      // This allows users to refill the form if they were denied
      if (result.status === APP_FLOW_STATUS.PREQUAL_APPROVED) {
        storePreQualifyResult(result);
        storeAppID(result.uuid);
        // Update form data with the current step if needed
        if (data.currentStep !== undefined) {
          storeFormFields((prev) => ({
            ...prev,
            currentStep: data.currentStep,
          }));
        }
      } else {
        storeFormFields(defaultPreQualifyValues);
      }

      // Redirect to result page with the application UUID
      navigate(`/prequalify-result/${result.uuid}`, { replace: true });
      window.scrollTo({ top: 0, behavior: "instant" });
    } catch (error) {
      // Show error modal
      logger.error("Error submitting form:", error);
      setShowErrorModal(true);
    }
  };

  const handleOnBlur = useCallback(
    async (event) => {
      const fieldName = event.target.name;
      if (!fieldName) return;

      const newValues = formMethods.getValues();
      const currentFields = fieldsForStep[currentStep];
      const fieldsToUpdate = [];

      if (fieldName === "businessStartYear") {
        fieldsToUpdate.push("businessStartDate");
      } else {
        fieldsToUpdate.push(fieldName);
      }

      for (const field of currentFields) {
        if (field !== fieldName && savedFields[field] !== newValues[field]) {
          fieldsToUpdate.push(field);
        }
      }

      await formMethods.trigger(fieldsToUpdate);

      // if the current field has error, track event
      const fieldHasError = formMethods.formState.errors[fieldName];
      if (fieldHasError) {
        trackCustomEvent("form_field_error", fieldName, false);
      }

      storeFormFields(newValues);
      fieldsToUpdate.forEach((field) => {
        trackFieldFilled(field, newValues[field], "PreQualification");
      });
    },
    [currentStep, formMethods, savedFields, storeFormFields],
  );

  // Prepare props for the presentation component
  const presentationProps = {
    header,
    subheader,
    firstStepTitle,
    steps,
    currentStep,
    formMethods,
    showErrorModal,
    errorMessage,
    errorId,
    formStatus,
    handleOnBlur,
    handleCloseErrorModal,
    handlePrevStep,
    handleNextStep,
    handleFormSubmit,
    switchToInvalidStep,
  };

  return <PreQualificationFormPresentation {...presentationProps} />;
};

/**
 * Presentation component that handles only the UI rendering for the pre-qualification form
 * @param {Object} props - Props passed from the container component
 * @returns {JSX.Element}
 */
const PreQualificationFormPresentation = ({
  header,
  subheader,
  firstStepTitle,
  steps,
  currentStep,
  formMethods,
  showErrorModal,
  errorMessage,
  errorId,
  formStatus,
  handleCloseErrorModal,
  handlePrevStep,
  handleNextStep,
  handleFormSubmit,
  handleOnBlur,
  switchToInvalidStep,
}) => {
  const formStartedRef = useRef(false);

  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);

  const handleCloseDuplicateWarning = () => {
    setShowDuplicateWarning(false);
  };

  // Render all steps but only show the current one
  // This prevents state reset when changing steps
  return (
    <div className="w-full">
      {/* Error Modal */}
      <ErrorModal
        isOpen={showErrorModal}
        error={errorMessage || "An error occurred. Please try again."}
        errorId={errorId || "UNKNOWN_ERROR"}
        onClose={handleCloseErrorModal}
      />

      <DuplicateSubmissionWarningModal isOpen={showDuplicateWarning} onClose={handleCloseDuplicateWarning} />

      <div className="bg-white rounded-sm shadow-lg p-4 sm:p-6 relative">
        {/* Loading Overlay */}
        <LoadingOverlay isLoading={formStatus === "saving"} message="Hang tight — we're reviewing your info." />

        <h2 className="text-3xl sm:text-4xl font-bold text-center text-blue-900 mb-6">{header}</h2>

        {subheader && (
          <h3 className="text-xl sm:text-2xl font-bold text-center text-blue-900 mt-2 mb-6 tracking-wide">
            {subheader}
          </h3>
        )}

        {/* Progress Bar */}
        <ProgressBar steps={steps} currentStep={currentStep} data-hj-allow />

        <form
          onSubmit={formMethods.handleSubmit(handleFormSubmit)}
          onBlur={handleOnBlur}
          onClickCapture={() => {
            if (formStartedRef.current) return;

            const recentApp = cookiesHandler.get(RECENT_APP);
            if (recentApp) {
              setShowDuplicateWarning(true);
            }

            formStartedRef.current = true;
            trackCustomEvent("prequal_form_started", true);
            const startedAt = sessionStorage.getItem("prequal_form_started_at");
            if (!startedAt) {
              sessionStorage.setItem("prequal_form_started_at", Date.now());
            }
          }}
        >
          {/* Step Content - Render all steps but only show the current one */}
          <div style={{ display: currentStep === 0 ? "block" : "none" }}>
            <FundingInformation control={formMethods.control} title={firstStepTitle} />
          </div>
          <div style={{ display: currentStep === 1 ? "block" : "none" }}>
            <BusinessInformation control={formMethods.control} formMethods={formMethods} />
          </div>
          <div style={{ display: currentStep === 2 ? "block" : "none" }}>
            <OwnerInformation control={formMethods.control} />
          </div>

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <div className="flex space-x-2">
              {currentStep > 0 && (
                <button
                  type="button"
                  onClick={handlePrevStep}
                  disabled={formStatus === "saving"}
                  className="bg-gray-500 hover:bg-gray-700 text-white font-base py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50"
                  data-hj-allow
                >
                  Previous
                </button>
              )}
            </div>

            {currentStep < steps.length - 1 ? (
              <button
                type="button"
                onClick={handleNextStep}
                disabled={formStatus === "saving"}
                className="bg-blue-500 hover:bg-blue-700 text-white font-base py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 ml-auto"
                data-hj-allow
              >
                Continue
              </button>
            ) : (
              <button
                type="button"
                onClick={async (...params) => {
                  sessionStorage.setItem("prequal_form_submitted_at", Date.now());
                  await switchToInvalidStep();
                  formMethods.handleSubmit(handleFormSubmit)(...params);
                }}
                disabled={formStatus === "saving"}
                className="bg-blue-500 hover:bg-blue-700 text-white font-base py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50 ml-auto"
                data-hj-allow
              >
                Submit
              </button>
            )}
          </div>
        </form>
      </div>
      <DataSecuredCard />
    </div>
  );
};
