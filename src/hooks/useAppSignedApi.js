import { useState, useCallback } from "react";
import { trackFormSubmitted } from "../utils/analytics";
import { API_ENDPOINT } from "../utils/consts";

const apiCache = {};

// API status enum
const STATUS = {
  IDLE: "idle",
  LOADING: "loading",
  SUCCESS: "success",
  ERROR: "error",
};

/**
 * Custom hook to manage the appSigned API call
 * This API is called when a user signs the application document
 * @returns {Object} API state and methods
 */
export function useAppSignedApi() {
  const [status, setStatus] = useState(STATUS.IDLE);
  const [error, setError] = useState({ message: null, id: null });
  const [result, setResult] = useState(null);

  // Derived state
  const isLoading = status === STATUS.LOADING;
  const isSuccess = status === STATUS.SUCCESS;
  const isError = status === STATUS.ERROR;
  const isIdle = status === STATUS.IDLE;

  /**
   * Notify the backend that the application has been signed
   * @param {string} uuid - Application ID that was signed
   * @returns {Promise<Object>} API response
   */
  const appSigned = useCallback(async (uuid, formData) => {
    let result_status = "unknown";
    let error_message = "";

    if (!uuid) {
      error_message = "No application ID provided";
      setError({
        message: error_message,
        id: "MISSING_APP_ID",
      });
      setStatus(STATUS.ERROR);
      throw new Error(error_message);
    }

    // Check cache first
    if (apiCache[uuid]) {
      setResult(apiCache[uuid]);
      setStatus(STATUS.SUCCESS);
      return apiCache[uuid];
    }

    setStatus(STATUS.LOADING);
    setError({ message: null, id: null });
    setResult(null);

    try {
      // Call the API directly using fetch
      const requestBody = {
        applicationFields: formData,
      };

      const response = await fetch(`${API_ENDPOINT}/app/${uuid}/sign`, {
        method: "POST",
        body: JSON.stringify(requestBody),
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw result;
      } else {
        result_status = "success";

        // Cache the successful result
        apiCache[uuid] = result;

        // Set the result
        setResult(result);
        setStatus(STATUS.SUCCESS);
      }

      // Return the response
      return result;
    } catch (error) {
      result_status = "error";
      error_message =
        error?.error || error?.message || "There was a problem updating the signed status. Please try again later.";
      const id = error?.errorId || "UNKNOWN_ERROR";
      setError({ message: error_message, id });
      setStatus(STATUS.ERROR);

      // Re-throw the error for the caller to handle
      throw error;
    } finally {
      trackFormSubmitted("Application", {
        event_name: "Signed Application",
        uuid,
        result_status,
        ...(result_status === "error" && { error_message }),
      });
    }
  }, []);

  /**
   * Reset the API state
   */
  const reset = useCallback(() => {
    setStatus(STATUS.IDLE);
    setError({ message: null, id: null });
    setResult(null);
  }, []);

  return {
    status,
    error: error.message,
    errorId: error.id,
    result,
    isLoading,
    isSuccess,
    isError,
    isIdle,
    appSigned,
    reset,
  };
}
